import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';

class ProductService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = AppConstants.productsCollection;
  static const Uuid _uuid = Uuid();

  /// Get all products with pagination and filters for admin
  static Future<List<ProductModel>> getAllProductsPaginated({
    DocumentSnapshot? lastDocument,
    int limit = 20,
    String? status, // 'all', 'available', 'unavailable', 'featured'
    String? category,
    String? sellerId,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply status filter
      if (status != null && status != 'all') {
        switch (status) {
          case 'available':
            query = query.where('isAvailable', isEqualTo: true);
            break;
          case 'unavailable':
            query = query.where('isAvailable', isEqualTo: false);
            break;
          case 'featured':
            query = query.where('isFeatured', isEqualTo: true);
            break;
        }
      }

      // Apply category filter
      if (category != null && category.isNotEmpty && category != 'all') {
        query = query.where('category', isEqualTo: category);
      }

      // Apply seller filter
      if (sellerId != null && sellerId.isNotEmpty) {
        query = query.where('sellerId', isEqualTo: sellerId);
      }

      // Order by creation date
      query = query.orderBy('createdAt', descending: true);

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      List<ProductModel> products = querySnapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();

      // Apply search filter if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        products = products.where((product) {
          return product.name.toLowerCase().contains(searchLower) ||
                 product.description.toLowerCase().contains(searchLower) ||
                 product.category.toLowerCase().contains(searchLower) ||
                 product.sellerName.toLowerCase().contains(searchLower) ||
                 product.tags.any((tag) => tag.toLowerCase().contains(searchLower));
        }).toList();
      }

      return products;
    } catch (e) {
      print('Error fetching products: $e');
      return [];
    }
  }

  /// Get product by ID
  static Future<ProductModel?> getProductById(String productId) async {
    try {
      final docSnapshot = await _firestore
          .collection(_collection)
          .doc(productId)
          .get();

      if (docSnapshot.exists) {
        return ProductModel.fromDocument(docSnapshot);
      }
      return null;
    } catch (e) {
      print('Error fetching product by ID: $e');
      return null;
    }
  }

  /// Update product availability
  static Future<bool> updateProductAvailability({
    required String productId,
    required bool isAvailable,
  }) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(productId)
          .update({
        'isAvailable': isAvailable,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error updating product availability: $e');
      return false;
    }
  }

  /// Update product featured status
  static Future<bool> updateProductFeaturedStatus({
    required String productId,
    required bool isFeatured,
  }) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(productId)
          .update({
        'isFeatured': isFeatured,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error updating product featured status: $e');
      return false;
    }
  }

  /// Update product category
  static Future<bool> updateProductCategory({
    required String productId,
    required String category,
  }) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(productId)
          .update({
        'category': category,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error updating product category: $e');
      return false;
    }
  }

  /// Delete product (soft delete)
  static Future<bool> deleteProduct(String productId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(productId)
          .update({
        'isAvailable': false,
        'isDeleted': true,
        'deletedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error deleting product: $e');
      return false;
    }
  }

  /// Permanently delete product
  static Future<bool> permanentlyDeleteProduct(String productId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(productId)
          .delete();

      return true;
    } catch (e) {
      print('Error permanently deleting product: $e');
      return false;
    }
  }

  /// Get products statistics
  static Future<Map<String, int>> getProductsStatistics() async {
    try {
      final allProductsSnapshot = await _firestore
          .collection(_collection)
          .get();

      final availableProductsSnapshot = await _firestore
          .collection(_collection)
          .where('isAvailable', isEqualTo: true)
          .get();

      final featuredProductsSnapshot = await _firestore
          .collection(_collection)
          .where('isFeatured', isEqualTo: true)
          .get();

      return {
        'total': allProductsSnapshot.docs.length,
        'available': availableProductsSnapshot.docs.length,
        'unavailable': allProductsSnapshot.docs.length - availableProductsSnapshot.docs.length,
        'featured': featuredProductsSnapshot.docs.length,
      };
    } catch (e) {
      print('Error fetching products statistics: $e');
      return {
        'total': 0,
        'available': 0,
        'unavailable': 0,
        'featured': 0,
      };
    }
  }

  /// Get all categories
  static Future<List<String>> getAllCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .get();

      final categories = <String>{};
      for (final doc in querySnapshot.docs) {
        final product = ProductModel.fromDocument(doc);
        if (product.category.isNotEmpty) {
          categories.add(product.category);
        }
      }

      final categoryList = categories.toList();
      categoryList.sort();
      return categoryList;
    } catch (e) {
      print('Error fetching categories: $e');
      return [];
    }
  }

  /// Get products by seller ID
  static Future<List<ProductModel>> getProductsBySellerId(String sellerId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('sellerId', isEqualTo: sellerId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching seller products: $e');
      return [];
    }
  }

  /// Bulk update products
  static Future<bool> bulkUpdateProducts({
    required List<String> productIds,
    required Map<String, dynamic> updateData,
  }) async {
    try {
      final batch = _firestore.batch();

      for (String productId in productIds) {
        final docRef = _firestore.collection(_collection).doc(productId);
        batch.update(docRef, {
          ...updateData,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error bulk updating products: $e');
      return false;
    }
  }

  /// Search products
  static Future<List<ProductModel>> searchProducts({
    required String query,
    int limit = 50,
  }) async {
    try {
      if (query.isEmpty) return [];

      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(limit * 2) // Get more to filter
          .get();

      final allProducts = querySnapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();

      // Filter products based on search query
      final searchLower = query.toLowerCase();
      return allProducts.where((product) {
        return product.name.toLowerCase().contains(searchLower) ||
               product.description.toLowerCase().contains(searchLower) ||
               product.category.toLowerCase().contains(searchLower) ||
               product.sellerName.toLowerCase().contains(searchLower) ||
               product.tags.any((tag) => tag.toLowerCase().contains(searchLower));
      }).take(limit).toList();
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  /// Get recent products stream for real-time updates
  static Stream<List<ProductModel>> getRecentProductsStream({int limit = 10}) {
    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();
    });
  }

  /// Get category statistics
  static Future<Map<String, int>> getCategoryStatistics() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .get();

      final categoryStats = <String, int>{};
      for (final doc in querySnapshot.docs) {
        final product = ProductModel.fromDocument(doc);
        if (product.category.isNotEmpty) {
          categoryStats[product.category] = (categoryStats[product.category] ?? 0) + 1;
        }
      }

      return categoryStats;
    } catch (e) {
      print('Error fetching category statistics: $e');
      return {};
    }
  }
}
