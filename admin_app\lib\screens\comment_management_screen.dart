import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/comment_model.dart';
import '../services/comment_service.dart';
import '../widgets/comment_management/comment_card_admin.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';

class CommentManagementScreen extends StatefulWidget {
  const CommentManagementScreen({super.key});

  @override
  State<CommentManagementScreen> createState() => _CommentManagementScreenState();
}

class _CommentManagementScreenState extends State<CommentManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Data
  List<CommentModel> _comments = [];
  bool _isLoading = false;
  bool _hasMoreComments = true;
  DocumentSnapshot? _lastDocument;
  String _currentFilter = 'all';
  String _searchQuery = '';

  // Statistics
  Map<String, int> _statistics = {
    'total': 0,
    'active': 0,
    'pending': 0,
    'inactive': 0,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _scrollController.addListener(_onScroll);
    _loadStatistics();
    _loadComments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      final filters = ['all', 'active', 'pending', 'inactive'];
      _currentFilter = filters[_tabController.index];
      _refreshComments();
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreComments();
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await CommentService.getCommentsStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
        });
      }
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  Future<void> _loadComments() async {
    if (_isLoading || !_hasMoreComments) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final comments = await CommentService.getAllCommentsPaginated(
        lastDocument: _lastDocument,
        status: _currentFilter,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      if (mounted) {
        setState(() {
          if (_lastDocument == null) {
            _comments = comments;
          } else {
            _comments.addAll(comments);
          }
          _hasMoreComments = comments.length >= 20;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('Error loading comments: $e');
    }
  }

  Future<void> _loadMoreComments() async {
    if (!_isLoading && _hasMoreComments) {
      await _loadComments();
    }
  }

  void _refreshComments() {
    setState(() {
      _comments.clear();
      _lastDocument = null;
      _hasMoreComments = true;
    });
    _loadComments();
    _loadStatistics();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _refreshComments();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          _buildSearchBar(),
          Expanded(
            child: _buildCommentsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.comment_outlined,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          const Text(
            'Comment Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeHeading,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: _refreshComments,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppConstants.surfaceColor,
      child: TabBar(
        controller: _tabController,
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        tabs: [
          Tab(
            text: 'All (${_statistics['total']})',
          ),
          Tab(
            text: 'Active (${_statistics['active']})',
          ),
          Tab(
            text: 'Pending (${_statistics['pending']})',
          ),
          Tab(
            text: 'Inactive (${_statistics['inactive']})',
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: AppConstants.surfaceColor,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search comments by content, username, or display name...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _onSearchChanged('');
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            borderSide: const BorderSide(color: AppConstants.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            borderSide: const BorderSide(color: AppConstants.primaryColor),
          ),
        ),
        onChanged: _onSearchChanged,
      ),
    );
  }

  Widget _buildCommentsList() {
    if (_isLoading && _comments.isEmpty) {
      return const LoadingWidget();
    }

    if (_comments.isEmpty && !_isLoading) {
      return EmptyStateWidget(
        icon: Icons.comment_outlined,
        title: 'No Comments Found',
        subtitle: _searchQuery.isNotEmpty
            ? 'No comments match your search criteria'
            : 'No comments available for the selected filter',
        actionText: 'Refresh',
        onAction: _refreshComments,
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshComments();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _comments.length + (_hasMoreComments ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _comments.length) {
            return const Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: CommentCardAdmin(
              comment: _comments[index],
              onCommentUpdated: () {
                _refreshComments();
              },
            ),
          );
        },
      ),
    );
  }
}
